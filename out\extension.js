"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const treeDataProvider_1 = require("./treeDataProvider");
const webviewProvider_1 = require("./webviewProvider");
const pythonServer_1 = require("./pythonServer");
const projectScanner_1 = require("./utils/projectScanner");
let pythonServer;
let projectProvider;
let webviewProvider;
async function activate(context) {
    console.log('Zephyr Clock Configurator extension is now active!');
    // Initialize Python server
    pythonServer = new pythonServer_1.PythonServerManager(context);
    await pythonServer.start();
    // Initialize project scanner
    const projectScanner = new projectScanner_1.ZephyrProjectScanner();
    // Initialize tree data provider
    projectProvider = new treeDataProvider_1.ZephyrProjectProvider(projectScanner);
    vscode.window.registerTreeDataProvider('zephyr-projects', projectProvider);
    // Initialize webview provider
    webviewProvider = new webviewProvider_1.ClockConfigWebviewProvider(context.extensionUri, pythonServer);
    // Register commands
    const refreshCommand = vscode.commands.registerCommand('zephyr.refreshProjects', () => {
        projectProvider.refresh();
    });
    const launchConfiguratorCommand = vscode.commands.registerCommand('zephyr.launchConfigurator', async (project) => {
        await webviewProvider.createOrShow(project);
    });
    const selectProjectCommand = vscode.commands.registerCommand('zephyr.selectProject', async (project) => {
        await webviewProvider.selectProject(project);
    });
    // Register file system watcher for Zephyr project files
    const watcher = vscode.workspace.createFileSystemWatcher('**/{CMakeLists.txt,prj.conf,*.dtsi,*.yaml}');
    watcher.onDidCreate(() => projectProvider.refresh());
    watcher.onDidDelete(() => projectProvider.refresh());
    watcher.onDidChange(() => projectProvider.refresh());
    context.subscriptions.push(refreshCommand, launchConfiguratorCommand, selectProjectCommand, watcher);
    // Auto-refresh projects on workspace change
    vscode.workspace.onDidChangeWorkspaceFolders(() => {
        projectProvider.refresh();
    });
    // Initial project scan
    await projectProvider.refresh();
}
exports.activate = activate;
async function deactivate() {
    console.log('Deactivating Zephyr Clock Configurator extension...');
    try {
        // Stop the Python server gracefully
        if (pythonServer) {
            await pythonServer.stop();
        }
        // Close any open webview panels
        if (webviewProvider) {
            webviewProvider.dispose();
        }
        console.log('Extension deactivated successfully');
    }
    catch (error) {
        console.error('Error during extension deactivation:', error);
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map