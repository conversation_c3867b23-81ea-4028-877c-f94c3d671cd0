{"version": 3, "file": "treeDataProvider.js", "sourceRoot": "", "sources": ["../src/treeDataProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAKjC,MAAa,qBAAqB;IAM9B,YAAoB,cAAoC;QAApC,mBAAc,GAAd,cAAc,CAAsB;QALhD,yBAAoB,GAAqE,IAAI,MAAM,CAAC,YAAY,EAA+C,CAAC;QAC/J,wBAAmB,GAA8D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAElH,aAAQ,GAAoB,EAAE,CAAC;IAEoB,CAAC;IAE5D,KAAK,CAAC,OAAO;QACT,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QAEpC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5B,8CAA8C,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC/E,CAAC;SACL;QAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,OAA0B;QAClC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAA2B;QACnC,IAAI,CAAC,OAAO,EAAE;YACV,+BAA+B;YAC/B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,iBAAiB,CACrE,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,WAAW,EACnB,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,OAAO,CACV,CAAC,CAAC,CAAC;SACP;aAAM;YACH,yCAAyC;YACzC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;SACpE;IACL,CAAC;IAEO,kBAAkB,CAAC,OAAsB;QAC7C,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAEzC,sBAAsB;QACtB,QAAQ,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAC/B,UAAU,OAAO,CAAC,WAAW,EAAE,EAC/B,EAAE,EACF,EAAE,EACF,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,OAAO,EACP,OAAO,CACV,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAClC,sBAAsB,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,EACjD,EAAE,EACF,EAAE,EACF,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,OAAO,EACP,aAAa,CAChB,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC3B;QAED,aAAa;QACb,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,MAAM,QAAQ,GAAG,IAAI,iBAAiB,CAClC,eAAe,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,EAC1C,EAAE,EACF,EAAE,EACF,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,OAAO,EACP,aAAa,CAChB,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC3B;QAED,gBAAgB;QAChB,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YACjC,MAAM,WAAW,GAAG,IAAI,iBAAiB,CACrC,kBAAkB,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,EAChD,EAAE,EACF,EAAE,EACF,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,OAAO,EACP,gBAAgB,CACnB,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC9B;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,WAAW;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,UAAU,CAAC,WAAmB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;IAC3D,CAAC;CACJ;AAvGD,sDAuGC;AAED,MAAa,iBAAkB,SAAQ,MAAM,CAAC,QAAQ;IAClD,YACoB,KAAa,EACb,WAAmB,EACnB,WAAmB,EACnB,gBAAiD,EACjD,OAAsB,EACtB,WAAmB,SAAS;QAE5C,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAPf,UAAK,GAAL,KAAK,CAAQ;QACb,gBAAW,GAAX,WAAW,CAAQ;QACnB,gBAAW,GAAX,WAAW,CAAQ;QACnB,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,YAAO,GAAP,OAAO,CAAe;QACtB,aAAQ,GAAR,QAAQ,CAAoB;QAI5C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,QAAQ,KAAK,SAAS,EAAE;YACxB,IAAI,CAAC,OAAO,GAAG;gBACX,OAAO,EAAE,sBAAsB;gBAC/B,KAAK,EAAE,gBAAgB;gBACvB,SAAS,EAAE,CAAC,OAAO,CAAC;aACvB,CAAC;SACL;IACL,CAAC;IAEO,UAAU;QACd,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACnB,KAAK,SAAS;gBACV,OAAO,mBAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAClH,KAAK,OAAO;gBACR,OAAO,wBAAwB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC9D,KAAK,aAAa;gBACd,OAAO,6BAA6B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;YAC/E,KAAK,aAAa;gBACd,OAAO,6BAA6B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,SAAS,CAAC;YAC/E,KAAK,gBAAgB;gBACjB,OAAO,kBAAkB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,SAAS,CAAC;YACvE;gBACI,OAAO,IAAI,CAAC,KAAK,CAAC;SACzB;IACL,CAAC;IAEO,cAAc;QAClB,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACnB,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;YACpC;gBACI,OAAO,EAAE,CAAC;SACjB;IACL,CAAC;IAEO,eAAe;QACnB,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACnB,KAAK,SAAS;gBACV,OAAO,eAAe,CAAC;YAC3B,KAAK,OAAO;gBACR,OAAO,aAAa,CAAC;YACzB,KAAK,aAAa;gBACd,OAAO,kBAAkB,CAAC;YAC9B,KAAK,aAAa;gBACd,OAAO,kBAAkB,CAAC;YAC9B,KAAK,gBAAgB;gBACjB,OAAO,qBAAqB,CAAC;YACjC;gBACI,OAAO,YAAY,CAAC;SAC3B;IACL,CAAC;IAEO,WAAW;QACf,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACnB,KAAK,SAAS;gBACV,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACjD,KAAK,OAAO;gBACR,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACjD,KAAK,aAAa;gBACd,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC7C,KAAK,aAAa;gBACd,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC7C,KAAK,gBAAgB;gBACjB,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACjD;gBACI,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAC3C;IACL,CAAC;CACJ;AApFD,8CAoFC"}