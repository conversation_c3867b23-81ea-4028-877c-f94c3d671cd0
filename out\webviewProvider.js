"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClockConfigWebviewProvider = void 0;
const vscode = __importStar(require("vscode"));
class ClockConfigWebviewProvider {
    constructor(extensionUri, pythonServer) {
        this.extensionUri = extensionUri;
        this.pythonServer = pythonServer;
    }
    async createOrShow(project) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;
        // If we already have a panel, show it
        if (this.panel) {
            this.panel.reveal(column);
            if (project) {
                await this.selectProject(project);
            }
            return;
        }
        // Otherwise, create a new panel
        this.panel = vscode.window.createWebviewPanel(ClockConfigWebviewProvider.viewType, 'Zephyr Clock Configurator', column || vscode.ViewColumn.One, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [
                vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist'),
                vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'public')
            ]
        });
        // Set the webview's initial html content
        this.panel.webview.html = this.getHtmlForWebview(this.panel.webview);
        // Handle messages from the webview
        this.panel.webview.onDidReceiveMessage(async (message) => {
            await this.handleWebviewMessage(message);
        }, undefined, []);
        // Handle panel disposal
        this.panel.onDidDispose(() => {
            this.panel = undefined;
        }, null, []);
        // Select project if provided
        if (project) {
            await this.selectProject(project);
        }
    }
    async selectProject(project) {
        this.selectedProject = project;
        if (this.panel) {
            // Send project selection to webview
            await this.panel.webview.postMessage({
                type: 'PROJECT_SELECTED',
                payload: {
                    project: project,
                    serverUrl: this.pythonServer.getServerUrl()
                }
            });
            // Update panel title
            this.panel.title = `Clock Config - ${project.name}`;
        }
    }
    async handleWebviewMessage(message) {
        switch (message.type) {
            case 'GET_CLOCK_CONFIG':
                await this.handleGetClockConfig(message.payload);
                break;
            case 'UPDATE_CLOCK_CONFIG':
                await this.handleUpdateClockConfig(message.payload);
                break;
            case 'SCAN_PROJECT':
                await this.handleScanProject(message.payload);
                break;
            case 'GET_SERVER_STATUS':
                await this.handleGetServerStatus();
                break;
            case 'WEBVIEW_READY':
                await this.handleWebviewReady();
                break;
            default:
                console.warn(`Unknown message type: ${message.type}`);
        }
    }
    async handleGetClockConfig(payload) {
        if (!this.selectedProject) {
            await this.sendError('No project selected');
            return;
        }
        try {
            const response = await this.pythonServer.makeRequest(`/projects/${encodeURIComponent(this.selectedProject.path)}/clock-config`);
            await this.panel?.webview.postMessage({
                type: 'CLOCK_CONFIG_DATA',
                payload: response
            });
        }
        catch (error) {
            await this.sendError(`Failed to get clock config: ${error}`);
        }
    }
    async handleUpdateClockConfig(payload) {
        if (!this.selectedProject) {
            await this.sendError('No project selected');
            return;
        }
        try {
            const response = await this.pythonServer.makeRequest(`/projects/${encodeURIComponent(this.selectedProject.path)}/clock-config`, 'POST', payload.config);
            await this.panel?.webview.postMessage({
                type: 'CLOCK_CONFIG_UPDATED',
                payload: response
            });
        }
        catch (error) {
            await this.sendError(`Failed to update clock config: ${error}`);
        }
    }
    async handleScanProject(payload) {
        if (!this.selectedProject) {
            await this.sendError('No project selected');
            return;
        }
        try {
            const response = await this.pythonServer.makeRequest(`/projects/scan`, 'POST', { projectPath: this.selectedProject.path });
            await this.panel?.webview.postMessage({
                type: 'PROJECT_SCANNED',
                payload: response
            });
        }
        catch (error) {
            await this.sendError(`Failed to scan project: ${error}`);
        }
    }
    async handleGetServerStatus() {
        const isRunning = this.pythonServer.isServerRunning();
        const serverUrl = this.pythonServer.getServerUrl();
        await this.panel?.webview.postMessage({
            type: 'SERVER_STATUS',
            payload: {
                isRunning,
                serverUrl
            }
        });
    }
    async handleWebviewReady() {
        // Send initial data when webview is ready
        await this.handleGetServerStatus();
        if (this.selectedProject) {
            await this.selectProject(this.selectedProject);
        }
    }
    async sendError(error) {
        await this.panel?.webview.postMessage({
            type: 'ERROR',
            payload: { error }
        });
    }
    getHtmlForWebview(webview) {
        // Get the local path to main script run in the webview
        const scriptPathOnDisk = vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist', 'assets');
        const scriptUri = webview.asWebviewUri(scriptPathOnDisk);
        // Get the local path to CSS file
        const stylePathOnDisk = vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist', 'assets');
        const styleUri = webview.asWebviewUri(stylePathOnDisk);
        // Use a nonce to only allow specific scripts to be run
        const nonce = this.getNonce();
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; connect-src http://127.0.0.1:8000 ws://127.0.0.1:8000;">
                <title>Zephyr Clock Configurator</title>
                <base href="${scriptUri}/">
            </head>
            <body>
                <div id="root"></div>
                <script type="module" nonce="${nonce}" src="${scriptUri}/index.js"></script>
            </body>
            </html>`;
    }
    getNonce() {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
    dispose() {
        if (this.panel) {
            this.panel.dispose();
            this.panel = undefined;
        }
    }
}
exports.ClockConfigWebviewProvider = ClockConfigWebviewProvider;
ClockConfigWebviewProvider.viewType = 'zephyr.clockConfig';
//# sourceMappingURL=webviewProvider.js.map