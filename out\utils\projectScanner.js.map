{"version": 3, "file": "projectScanner.js", "sourceRoot": "", "sources": ["../../src/utils/projectScanner.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AAGzB,MAAa,oBAAoB;IAAjC;QACqB,sBAAiB,GAAG;YACjC,gBAAgB;YAChB,UAAU;YACV,SAAS;SACZ,CAAC;QAEe,0BAAqB,GAAG;YACrC,qBAAqB;YACrB,wBAAwB;YACxB,cAAc;SACjB,CAAC;IAsKN,CAAC;IApKG,KAAK,CAAC,aAAa;QACf,MAAM,QAAQ,GAAoB,EAAE,CAAC;QACrC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACpC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,4BAA4B,CAAC,EAAE,CAAC;SAC/D;QAED,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACpD,IAAI;gBACA,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAChE,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;aACpC;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,CAAC,IAAI,CAAC,yBAAyB,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;aACjE;SACJ;QAED,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,UAAkB;QACvC,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,8DAA8D;QAC9D,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC/C,IAAI,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAC3D,oBAAoB,CACvB,CAAC;QAEF,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAElD,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE;gBACxC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBACtD,IAAI,OAAO,EAAE;oBACT,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC1B;aACJ;SACJ;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,WAAmB;QAC7C,IAAI;YACA,+CAA+C;YAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;gBAC1B,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACnD,IAAI,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAC;oBACvC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC;oBACtC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;oBAC7B,OAAO,IAAI,CAAC;iBACf;aACJ;YAED,0BAA0B;YAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YACvD,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;gBAC5B,OAAO,IAAI,CAAC;aACf;YAED,0BAA0B;YAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACtD,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;gBAC5B,OAAO,IAAI,CAAC;aACf;YAED,OAAO,KAAK,CAAC;SAChB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,qBAAqB,WAAW,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9E,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,WAAmB;QAC5C,IAAI;YACA,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAE/C,yBAAyB;YACzB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAEvE,kBAAkB;YAClB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACjE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAE/D,qBAAqB;YACrB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAEtE,gCAAgC;YAChC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAEjE,MAAM,OAAO,GAAkB;gBAC3B,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,WAAW;gBACjB,WAAW;gBACX,SAAS,EAAE,CAAC,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC;gBAC1C,SAAS,EAAE,CAAC,GAAG,SAAS,EAAE,GAAG,QAAQ,CAAC;gBACtC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC;gBACnD,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC;gBAC/C,YAAY;aACf,CAAC;YAEF,OAAO,OAAO,CAAC;SAClB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,2BAA2B,WAAW,GAAG,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,OAAe;QACrD,IAAI;YACA,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC1C,IAAI,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,EAC7C,oBAAoB,CACvB,CAAC;YACF,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,oCAAoC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,WAAmB;QAClD,IAAI;YACA,+CAA+C;YAC/C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;YAC5E,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAChC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACpD,OAAO,SAAS,CAAC;aACpB;YAED,+CAA+C;YAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YAC3D,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;gBAC1B,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACnD,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC7D,IAAI,UAAU,EAAE;oBACZ,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;iBACxB;aACJ;YAED,kDAAkD;YAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YACpD,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBACzB,uCAAuC;gBACvC,mCAAmC;aACtC;YAED,OAAO,SAAS,CAAC;SACpB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,sCAAsC,WAAW,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,OAAO,SAAS,CAAC;SACpB;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,WAAmB;QACvC,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE;YACzC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;SACjD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAjLD,oDAiLC"}