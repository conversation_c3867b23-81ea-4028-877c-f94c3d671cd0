{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../../src/test/suite/extension.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AACjC,+DAAkE;AAElE,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;IAEzD,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;QACzC,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;QAC9E,IAAI,SAAS,EAAE;YACX,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SACjC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEzD,MAAM,gBAAgB,GAAG;YACrB,wBAAwB;YACxB,2BAA2B;YAC3B,sBAAsB;SACzB,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE;YACpC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,WAAW,OAAO,uBAAuB,CAAC,CAAC;SACpF;IACL,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AAEH,KAAK,CAAC,4BAA4B,EAAE,GAAG,EAAE;IACrC,IAAI,OAA6B,CAAC;IAElC,KAAK,CAAC,GAAG,EAAE;QACP,OAAO,GAAG,IAAI,qCAAoB,EAAE,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;QACrD,uBAAuB;QACvB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,EAAE,CAAC;QAC7C,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAClB,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;QAC/D,mDAAmD;QACnD,2EAA2E;QAC3E,MAAM,eAAe,GAAG,oBAAoB,CAAC;QAE7C,IAAI;YACA,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YACjE,2CAA2C;YAC3C,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACrC;QAAC,OAAO,KAAK,EAAE;YACZ,iCAAiC;YACjC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;SACnB;IACL,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}