{"version": 3, "file": "fileWatcher.js", "sourceRoot": "", "sources": ["../../src/utils/fileWatcher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAG7B,MAAa,iBAAiB;IAI1B;QAHQ,aAAQ,GAA+B,EAAE,CAAC;QAI9C,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,aAAa;QACjB,mCAAmC;QACnC,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC;QACnF,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3D,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3D,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEjC,6BAA6B;QAC7B,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;QAC/E,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEnC,8BAA8B;QAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAC1E,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEhC,0BAA0B;QAC1B,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QAChF,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEnC,uBAAuB;QACvB,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;QAChF,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEhC,0BAA0B;QAC1B,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;QAC/E,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAe;QAC1C,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,+CAA+C;YAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACrD,IAAI,WAAW,EAAE;gBACb,mDAAmD;gBACnD,MAAM,OAAO,GAAkB;oBAC3B,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;oBAChC,IAAI,EAAE,WAAW;oBACjB,WAAW,EAAE,SAAS;oBACtB,SAAS,EAAE,EAAE;oBACb,SAAS,EAAE,EAAE;oBACb,YAAY,EAAE,EAAE;iBACnB,CAAC;gBAEF,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;aAC1C;SACJ;IACL,CAAC;IAEO,eAAe,CAAC,QAAgB;QACpC,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAExC,OAAO,UAAU,KAAK,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC5C,6DAA6D;YAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;YAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAEtD,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;gBAC9E,OAAO,UAAU,CAAC;aACrB;YAED,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SACzC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB,CAAC,QAA0C;QACvD,IAAI,CAAC,wBAAwB,GAAG,QAAQ,CAAC;IAC7C,CAAC;IAED,OAAO;QACH,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACvB,CAAC;CACJ;AApGD,8CAoGC"}