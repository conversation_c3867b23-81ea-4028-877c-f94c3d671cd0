"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZephyrProjectScanner = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
class ZephyrProjectScanner {
    constructor() {
        this.ZEPHYR_INDICATORS = [
            'CMakeLists.txt',
            'prj.conf',
            'Kconfig'
        ];
        this.BOARD_CONFIG_PATTERNS = [
            '**/boards/**/*.conf',
            '**/boards/**/*.overlay',
            '**/*.overlay'
        ];
    }
    async scanWorkspace() {
        const projects = [];
        const errors = [];
        if (!vscode.workspace.workspaceFolders) {
            return { projects, errors: ['No workspace folders found'] };
        }
        for (const folder of vscode.workspace.workspaceFolders) {
            try {
                const folderProjects = await this.scanFolder(folder.uri.fsPath);
                projects.push(...folderProjects);
            }
            catch (error) {
                errors.push(`Error scanning folder ${folder.name}: ${error}`);
            }
        }
        return { projects, errors };
    }
    async scanFolder(folderPath) {
        const projects = [];
        // Look for CMakeLists.txt files that indicate Zephyr projects
        const cmakeFiles = await vscode.workspace.findFiles(new vscode.RelativePattern(folderPath, '**/CMakeLists.txt'), '**/node_modules/**');
        for (const cmakeFile of cmakeFiles) {
            const projectDir = path.dirname(cmakeFile.fsPath);
            if (await this.isZephyrProject(projectDir)) {
                const project = await this.analyzeProject(projectDir);
                if (project) {
                    projects.push(project);
                }
            }
        }
        return projects;
    }
    async isZephyrProject(projectPath) {
        try {
            // Check for CMakeLists.txt with Zephyr content
            const cmakeFile = path.join(projectPath, 'CMakeLists.txt');
            if (fs.existsSync(cmakeFile)) {
                const content = fs.readFileSync(cmakeFile, 'utf8');
                if (content.includes('find_package(Zephyr') ||
                    content.includes('zephyr_get_targets') ||
                    content.includes('CONFIG_')) {
                    return true;
                }
            }
            // Check for prj.conf file
            const prjConfFile = path.join(projectPath, 'prj.conf');
            if (fs.existsSync(prjConfFile)) {
                return true;
            }
            // Check for Kconfig files
            const kconfigFile = path.join(projectPath, 'Kconfig');
            if (fs.existsSync(kconfigFile)) {
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Error checking if ${projectPath} is a Zephyr project:`, error);
            return false;
        }
    }
    async analyzeProject(projectPath) {
        try {
            const projectName = path.basename(projectPath);
            // Find device tree files
            const dtsiFiles = await this.findFiles(projectPath, '**/*.dtsi');
            const overlayFiles = await this.findFiles(projectPath, '**/*.overlay');
            // Find YAML files
            const yamlFiles = await this.findFiles(projectPath, '**/*.yaml');
            const ymlFiles = await this.findFiles(projectPath, '**/*.yml');
            // Find Kconfig files
            const kconfigFiles = await this.findFiles(projectPath, '**/Kconfig*');
            // Determine board configuration
            const boardConfig = await this.determineBoardConfig(projectPath);
            const project = {
                name: projectName,
                path: projectPath,
                boardConfig,
                dtsiFiles: [...dtsiFiles, ...overlayFiles],
                yamlFiles: [...yamlFiles, ...ymlFiles],
                cmakeFile: path.join(projectPath, 'CMakeLists.txt'),
                prjConfFile: path.join(projectPath, 'prj.conf'),
                kconfigFiles
            };
            return project;
        }
        catch (error) {
            console.error(`Error analyzing project ${projectPath}:`, error);
            return null;
        }
    }
    async findFiles(basePath, pattern) {
        try {
            const files = await vscode.workspace.findFiles(new vscode.RelativePattern(basePath, pattern), '**/node_modules/**');
            return files.map(file => file.fsPath);
        }
        catch (error) {
            console.error(`Error finding files with pattern ${pattern}:`, error);
            return [];
        }
    }
    async determineBoardConfig(projectPath) {
        try {
            // Check for board-specific configuration files
            const boardFiles = await this.findFiles(projectPath, '**/boards/**/*.conf');
            if (boardFiles.length > 0) {
                const boardFile = boardFiles[0];
                const boardName = path.basename(boardFile, '.conf');
                return boardName;
            }
            // Check CMakeLists.txt for board specification
            const cmakeFile = path.join(projectPath, 'CMakeLists.txt');
            if (fs.existsSync(cmakeFile)) {
                const content = fs.readFileSync(cmakeFile, 'utf8');
                const boardMatch = content.match(/set\(BOARD\s+([^\s)]+)\)/);
                if (boardMatch) {
                    return boardMatch[1];
                }
            }
            // Check for west.yml or other configuration files
            const westFile = path.join(projectPath, 'west.yml');
            if (fs.existsSync(westFile)) {
                // Parse west.yml for board information
                // This would require a YAML parser
            }
            return 'unknown';
        }
        catch (error) {
            console.error(`Error determining board config for ${projectPath}:`, error);
            return 'unknown';
        }
    }
    async getProjectDetails(projectPath) {
        if (await this.isZephyrProject(projectPath)) {
            return await this.analyzeProject(projectPath);
        }
        return null;
    }
}
exports.ZephyrProjectScanner = ZephyrProjectScanner;
//# sourceMappingURL=projectScanner.js.map