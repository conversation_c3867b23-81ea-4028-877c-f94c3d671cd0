"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZephyrFileWatcher = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class ZephyrFileWatcher {
    constructor() {
        this.watchers = [];
        this.setupWatchers();
    }
    setupWatchers() {
        // Watch for CMakeLists.txt changes
        const cmakeWatcher = vscode.workspace.createFileSystemWatcher('**/CMakeLists.txt');
        cmakeWatcher.onDidChange(this.handleFileChange.bind(this));
        cmakeWatcher.onDidCreate(this.handleFileChange.bind(this));
        cmakeWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(cmakeWatcher);
        // Watch for prj.conf changes
        const prjConfWatcher = vscode.workspace.createFileSystemWatcher('**/prj.conf');
        prjConfWatcher.onDidChange(this.handleFileChange.bind(this));
        prjConfWatcher.onDidCreate(this.handleFileChange.bind(this));
        prjConfWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(prjConfWatcher);
        // Watch for device tree files
        const dtsiWatcher = vscode.workspace.createFileSystemWatcher('**/*.dtsi');
        dtsiWatcher.onDidChange(this.handleFileChange.bind(this));
        dtsiWatcher.onDidCreate(this.handleFileChange.bind(this));
        dtsiWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(dtsiWatcher);
        // Watch for overlay files
        const overlayWatcher = vscode.workspace.createFileSystemWatcher('**/*.overlay');
        overlayWatcher.onDidChange(this.handleFileChange.bind(this));
        overlayWatcher.onDidCreate(this.handleFileChange.bind(this));
        overlayWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(overlayWatcher);
        // Watch for YAML files
        const yamlWatcher = vscode.workspace.createFileSystemWatcher('**/*.{yaml,yml}');
        yamlWatcher.onDidChange(this.handleFileChange.bind(this));
        yamlWatcher.onDidCreate(this.handleFileChange.bind(this));
        yamlWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(yamlWatcher);
        // Watch for Kconfig files
        const kconfigWatcher = vscode.workspace.createFileSystemWatcher('**/Kconfig*');
        kconfigWatcher.onDidChange(this.handleFileChange.bind(this));
        kconfigWatcher.onDidCreate(this.handleFileChange.bind(this));
        kconfigWatcher.onDidDelete(this.handleFileChange.bind(this));
        this.watchers.push(kconfigWatcher);
    }
    async handleFileChange(uri) {
        console.log(`File changed: ${uri.fsPath}`);
        if (this.onProjectChangedCallback) {
            // Determine which project this file belongs to
            const projectPath = this.findProjectRoot(uri.fsPath);
            if (projectPath) {
                // Create a minimal project object for the callback
                const project = {
                    name: path.basename(projectPath),
                    path: projectPath,
                    boardConfig: 'unknown',
                    dtsiFiles: [],
                    yamlFiles: [],
                    kconfigFiles: []
                };
                this.onProjectChangedCallback(project);
            }
        }
    }
    findProjectRoot(filePath) {
        let currentDir = path.dirname(filePath);
        while (currentDir !== path.dirname(currentDir)) {
            // Check if this directory contains Zephyr project indicators
            const cmakeFile = path.join(currentDir, 'CMakeLists.txt');
            const prjConfFile = path.join(currentDir, 'prj.conf');
            if (require('fs').existsSync(cmakeFile) || require('fs').existsSync(prjConfFile)) {
                return currentDir;
            }
            currentDir = path.dirname(currentDir);
        }
        return null;
    }
    onProjectChanged(callback) {
        this.onProjectChangedCallback = callback;
    }
    dispose() {
        this.watchers.forEach(watcher => watcher.dispose());
        this.watchers = [];
    }
}
exports.ZephyrFileWatcher = ZephyrFileWatcher;
//# sourceMappingURL=fileWatcher.js.map