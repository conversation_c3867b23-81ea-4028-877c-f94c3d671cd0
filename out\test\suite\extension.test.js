"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const projectScanner_1 = require("../../utils/projectScanner");
suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');
    test('Extension should be present', () => {
        assert.ok(vscode.extensions.getExtension('zephyr-clock-configurator'));
    });
    test('Extension should activate', async () => {
        const extension = vscode.extensions.getExtension('zephyr-clock-configurator');
        if (extension) {
            await extension.activate();
            assert.ok(extension.isActive);
        }
    });
    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        const expectedCommands = [
            'zephyr.refreshProjects',
            'zephyr.launchConfigurator',
            'zephyr.selectProject'
        ];
        for (const command of expectedCommands) {
            assert.ok(commands.includes(command), `Command ${command} should be registered`);
        }
    });
});
suite('Project Scanner Test Suite', () => {
    let scanner;
    setup(() => {
        scanner = new projectScanner_1.ZephyrProjectScanner();
    });
    test('Scanner should initialize', () => {
        assert.ok(scanner);
    });
    test('Scanner should handle empty workspace', async () => {
        // Mock empty workspace
        const result = await scanner.scanWorkspace();
        assert.ok(result);
        assert.ok(Array.isArray(result.projects));
        assert.ok(Array.isArray(result.errors));
    });
    test('Scanner should detect Zephyr project indicators', async () => {
        // This would require setting up a mock file system
        // For now, just test that the method exists and returns expected structure
        const mockProjectPath = '/mock/project/path';
        try {
            const details = await scanner.getProjectDetails(mockProjectPath);
            // Should return null for non-existent path
            assert.strictEqual(details, null);
        }
        catch (error) {
            // Expected for non-existent path
            assert.ok(true);
        }
    });
});
//# sourceMappingURL=extension.test.js.map