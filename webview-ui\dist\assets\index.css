:root{font-family:Inter,system-ui,Avenir,Helvetica,Arial,sans-serif;line-height:1.5;font-weight:400;color-scheme:light dark;color:var(--vscode-foreground);background-color:var(--vscode-editor-background);font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-text-size-adjust:100%}*{box-sizing:border-box}body{margin:0;padding:0;min-height:100vh;background-color:var(--vscode-editor-background);color:var(--vscode-foreground)}#root{width:100%;min-height:100vh}.container{padding:16px;max-width:800px;margin:0 auto}.header{display:flex;align-items:center;gap:12px;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--vscode-panel-border)}.header h1{margin:0;font-size:24px;font-weight:600}.project-info{background-color:var(--vscode-editor-inactiveSelectionBackground);border:1px solid var(--vscode-panel-border);border-radius:6px;padding:16px;margin-bottom:24px}.project-info h2{margin:0 0 8px;font-size:18px;font-weight:500}.project-info p{margin:4px 0;color:var(--vscode-descriptionForeground)}.clock-config-form{background-color:var(--vscode-editor-inactiveSelectionBackground);border:1px solid var(--vscode-panel-border);border-radius:6px;padding:24px}.form-group{margin-bottom:20px}.form-group label{display:block;margin-bottom:8px;font-weight:500;color:var(--vscode-foreground)}.form-group input,.form-group select{width:100%;padding:8px 12px;border:1px solid var(--vscode-input-border);border-radius:4px;background-color:var(--vscode-input-background);color:var(--vscode-input-foreground);font-size:14px}.form-group input:focus,.form-group select:focus{outline:none;border-color:var(--vscode-focusBorder);box-shadow:0 0 0 1px var(--vscode-focusBorder)}.checkbox-group{display:flex;align-items:center;gap:8px}.checkbox-group input[type=checkbox]{width:auto;margin:0}.frequency-group{display:flex;gap:12px;align-items:end}.frequency-input{flex:1}.frequency-unit{min-width:80px}.button-group{display:flex;gap:12px;margin-top:24px}.btn{padding:8px 16px;border:1px solid var(--vscode-button-border);border-radius:4px;background-color:var(--vscode-button-background);color:var(--vscode-button-foreground);cursor:pointer;font-size:14px;font-weight:500;transition:background-color .2s}.btn:hover{background-color:var(--vscode-button-hoverBackground)}.btn:disabled{opacity:.5;cursor:not-allowed}.btn-primary{background-color:var(--vscode-button-background);color:var(--vscode-button-foreground)}.btn-secondary{background-color:var(--vscode-button-secondaryBackground);color:var(--vscode-button-secondaryForeground)}.error-message{background-color:var(--vscode-inputValidation-errorBackground);border:1px solid var(--vscode-inputValidation-errorBorder);color:var(--vscode-inputValidation-errorForeground);padding:12px;border-radius:4px;margin-bottom:16px}.loading-spinner{display:inline-block;width:16px;height:16px;border:2px solid var(--vscode-progressBar-background);border-radius:50%;border-top-color:var(--vscode-progressBar-foreground);animation:spin 1s ease-in-out infinite}@keyframes spin{to{transform:rotate(360deg)}}.status-indicator{display:flex;align-items:center;gap:8px;padding:8px 12px;border-radius:4px;font-size:14px;margin-bottom:16px}.status-indicator.connected{background-color:var(--vscode-testing-iconPassed);color:var(--vscode-foreground)}.status-indicator.disconnected{background-color:var(--vscode-testing-iconFailed);color:var(--vscode-foreground)}.status-dot{width:8px;height:8px;border-radius:50%;background-color:currentColor}
