"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZephyrProjectItem = exports.ZephyrProjectProvider = void 0;
const vscode = __importStar(require("vscode"));
class ZephyrProjectProvider {
    constructor(projectScanner) {
        this.projectScanner = projectScanner;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.projects = [];
    }
    async refresh() {
        const scanResult = await this.projectScanner.scanWorkspace();
        this.projects = scanResult.projects;
        if (scanResult.errors.length > 0) {
            vscode.window.showWarningMessage(`Zephyr project scan completed with errors: ${scanResult.errors.join(', ')}`);
        }
        this._onDidChangeTreeData.fire();
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            // Root level - return projects
            return Promise.resolve(this.projects.map(project => new ZephyrProjectItem(project.name, project.path, project.boardConfig, vscode.TreeItemCollapsibleState.Collapsed, project)));
        }
        else {
            // Project level - return project details
            return Promise.resolve(this.getProjectChildren(element.project));
        }
    }
    getProjectChildren(project) {
        const children = [];
        // Board configuration
        children.push(new ZephyrProjectItem(`Board: ${project.boardConfig}`, '', '', vscode.TreeItemCollapsibleState.None, project, 'board'));
        // Device tree files
        if (project.dtsiFiles.length > 0) {
            const dtsiItem = new ZephyrProjectItem(`Device Tree Files (${project.dtsiFiles.length})`, '', '', vscode.TreeItemCollapsibleState.Collapsed, project, 'dtsi-folder');
            children.push(dtsiItem);
        }
        // YAML files
        if (project.yamlFiles.length > 0) {
            const yamlItem = new ZephyrProjectItem(`YAML Files (${project.yamlFiles.length})`, '', '', vscode.TreeItemCollapsibleState.Collapsed, project, 'yaml-folder');
            children.push(yamlItem);
        }
        // Kconfig files
        if (project.kconfigFiles.length > 0) {
            const kconfigItem = new ZephyrProjectItem(`Kconfig Files (${project.kconfigFiles.length})`, '', '', vscode.TreeItemCollapsibleState.Collapsed, project, 'kconfig-folder');
            children.push(kconfigItem);
        }
        return children;
    }
    getProjects() {
        return this.projects;
    }
    getProject(projectPath) {
        return this.projects.find(p => p.path === projectPath);
    }
}
exports.ZephyrProjectProvider = ZephyrProjectProvider;
class ZephyrProjectItem extends vscode.TreeItem {
    constructor(label, projectPath, boardConfig, collapsibleState, project, itemType = 'project') {
        super(label, collapsibleState);
        this.label = label;
        this.projectPath = projectPath;
        this.boardConfig = boardConfig;
        this.collapsibleState = collapsibleState;
        this.project = project;
        this.itemType = itemType;
        this.tooltip = this.getTooltip();
        this.description = this.getDescription();
        this.contextValue = this.getContextValue();
        this.iconPath = this.getIconPath();
        if (itemType === 'project') {
            this.command = {
                command: 'zephyr.selectProject',
                title: 'Select Project',
                arguments: [project]
            };
        }
    }
    getTooltip() {
        switch (this.itemType) {
            case 'project':
                return `Zephyr Project: ${this.project.name}\nPath: ${this.project.path}\nBoard: ${this.project.boardConfig}`;
            case 'board':
                return `Board Configuration: ${this.project.boardConfig}`;
            case 'dtsi-folder':
                return `Device Tree Source Files (${this.project.dtsiFiles.length} files)`;
            case 'yaml-folder':
                return `YAML Configuration Files (${this.project.yamlFiles.length} files)`;
            case 'kconfig-folder':
                return `Kconfig Files (${this.project.kconfigFiles.length} files)`;
            default:
                return this.label;
        }
    }
    getDescription() {
        switch (this.itemType) {
            case 'project':
                return this.project.boardConfig;
            default:
                return '';
        }
    }
    getContextValue() {
        switch (this.itemType) {
            case 'project':
                return 'zephyrProject';
            case 'board':
                return 'zephyrBoard';
            case 'dtsi-folder':
                return 'zephyrDtsiFolder';
            case 'yaml-folder':
                return 'zephyrYamlFolder';
            case 'kconfig-folder':
                return 'zephyrKconfigFolder';
            default:
                return 'zephyrItem';
        }
    }
    getIconPath() {
        switch (this.itemType) {
            case 'project':
                return new vscode.ThemeIcon('folder-opened');
            case 'board':
                return new vscode.ThemeIcon('circuit-board');
            case 'dtsi-folder':
                return new vscode.ThemeIcon('file-code');
            case 'yaml-folder':
                return new vscode.ThemeIcon('file-text');
            case 'kconfig-folder':
                return new vscode.ThemeIcon('settings-gear');
            default:
                return new vscode.ThemeIcon('file');
        }
    }
}
exports.ZephyrProjectItem = ZephyrProjectItem;
//# sourceMappingURL=treeDataProvider.js.map